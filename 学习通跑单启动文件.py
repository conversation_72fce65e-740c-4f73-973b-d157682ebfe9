# -*- coding: utf-8 -*-
import threading
import datetime
import concurrent.futures
import queue
import signal
import sys
from data.Exam import EXAM
from Config.UserSql import OrderProcessorsql
from API.TaskDo import Task
from data.Porgres import StartProces
from API.Session import StartSession
from Crypto.Cipher import AES
from Crypto.Util.Padding import pad
from base64 import b64encode
from loguru import logger
from bs4 import BeautifulSoup
import random
import re
import string
import time
import requests
import parsel
import json
import traceback

# 尝试导入HomeworkAI模块
try:
    from API.HomeworkAI import HomeworkAI

    HOMEWORK_AI_AVAILABLE = True
except ImportError:
    HOMEWORK_AI_AVAILABLE = False
    logger.warning("HomeworkAI模块未找到，AI自动填充功能不可用")


def encrypt_by_aes(message):
    key = "u2oh6Vu^HWe4_AES"
    # 确保密钥长度为AES密钥要求（16, 24, 32）
    key = key.encode("utf-8")  # 将密钥转换为字节
    iv = key  # 在这个例子中，我们使用密钥作为初始化向量，但这不是最佳实践
    cipher = AES.new(key, AES.MODE_CBC, iv)
    padded_data = pad(message.encode("utf-8"), AES.block_size)
    encrypted_data = cipher.encrypt(padded_data)
    return b64encode(encrypted_data).decode("utf-8")


def t():
    return int(time.time() * 1000)


# 全局变量
MAX_WORKERS = 100  # 最大线程数
active_threads = {}  # 存储活跃线程
thread_lock = threading.RLock()  # 线程锁
running = True  # 控制程序运行
account_locks = {}  # 账号锁，确保同一账号不会并发运行
pending_orders = queue.Queue()  # 待处理订单队列


# 线程管理类
class ThreadManager:
    @staticmethod
    def register_thread(oid, thread):
        with thread_lock:
            active_threads[oid] = thread
            logger.info(f"注册线程: OID={oid}, 当前活跃线程数: {len(active_threads)}")

    @staticmethod
    def unregister_thread(oid):
        with thread_lock:
            if oid in active_threads:
                del active_threads[oid]
                logger.info(
                    f"注销线程: OID={oid}, 当前活跃线程数: {len(active_threads)}"
                )

    @staticmethod
    def get_active_count():
        with thread_lock:
            return len(active_threads)

    @staticmethod
    def account_lock(username, cid):
        """获取账号锁，确保同一账号同一平台不会并发运行"""
        key = f"{username}_{cid}"
        with thread_lock:
            if key not in account_locks:
                account_locks[key] = threading.Lock()
            return account_locks[key].acquire(blocking=False)  # 非阻塞获取锁

    @staticmethod
    def account_unlock(username, cid):
        """释放账号锁"""
        key = f"{username}_{cid}"
        with thread_lock:
            if key in account_locks and account_locks[key].locked():
                account_locks[key].release()


# 信号处理函数
def signal_handler(sig, frame):
    global running
    logger.warning("接收到终止信号，正在安全关闭...")
    running = False
    sys.exit(0)


# 注册信号处理
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)


class UA:
    def __init__(self):
        self.WEB = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/117.0.0.0 Safari/537.36 Edg/117.0.2045.36"
        self.APP = {
            "User-Agent": f"/2.1.0 (Linux; U; Android 7.1.2; SM-G977N Build/LMY48Z) com.chaoxing.mobile/ChaoXingStudy_3_4.3.4_android_phone_494_27 (@Kalimdor)_{''.join(random.choice(string.hexdigits[:-6]) for _ in range(32))}"
        }


class MainXxt:
    def __init__(self, session, school, username, password, courseid, oid, cid, pool):
        self.session = session
        self.school = school
        self.pool = pool
        self.username = username
        self.password = password
        self.courseid = courseid
        self.oid = oid
        self.cid = cid
        self.retry_count = 0
        self.max_retries = 3
        self.is_running = True
        self.real_titles = {}  # 用于记录作业ID和真实标题的映射

    def handle_error(self, error_msg, update_status=True):
        """统一处理错误，支持重试机制"""
        self.retry_count += 1
        if self.retry_count <= self.max_retries:
            logger.warning(
                f"ID:{self.username}, 遇到错误: {error_msg}, 第{self.retry_count}次重试..."
            )
            time.sleep(2 * self.retry_count)  # 指数退避
            return False  # 继续重试
        else:
            logger.error(
                f"ID:{self.username}, 遇到错误: {error_msg}, 重试{self.max_retries}次后失败"
            )
            if update_status:
                self.pool.update_order(
                    f"update qingka_wangke_order set status = '异常' , process = '0%',remarks = '{error_msg}' where oid = '{self.oid}'"
                )
            return True  # 停止重试

    def fanyalogin(self):
        try:
            pattern = r"^1[3456789]\d{9}$"
            # 使用 re.match() 函数进行匹配
            if re.match(pattern, self.username):
                res = self.session.post(
                    "https://passport2.chaoxing.com/fanyalogin",
                    params={
                        "fid": "-1",
                        "uname": encrypt_by_aes(self.username),
                        "password": encrypt_by_aes(self.password),
                        "refer": "http%3A%2F%2Fi.mooc.chaoxing.com",
                        "t": "true",
                        "forbidotherlogin": "0",
                        "validate": "",
                        "doubleFactorLogin": "0",
                        "independentId": "0",
                        "independentNameId": "0",
                    },
                    timeout=30,  # 设置30秒超时
                ).json()
                if res["status"] is True:
                    return True
                else:
                    return res["msg2"]
            else:
                schoolcode = self.session.get(
                    f"https://passport2.chaoxing.com/org/searchUnis?filter={self.school}",
                    timeout=30,
                ).json()
                if schoolcode["result"]:
                    for i in schoolcode["froms"]:
                        schoolid = i["schoolid"]
                        api = f"https://passport2-api.chaoxing.com/v6/idNumberLogin?fid={schoolid}&idNumber={self.username}?pwd={self.password}&t=0"
                        res = self.session.post(api, timeout=30).json()
                        try:
                            sta = res["status"]
                        except:
                            sta = res["result"]
                        if sta is True:
                            return True
                        else:
                            pass
                    return res["msg"]
                else:
                    return "SchoolID获取失败 请检查是否为学校错误"
        except requests.exceptions.Timeout:
            error_msg = "登录超时，网络可能不稳定"
            if not self.handle_error(error_msg, update_status=False):
                return self.fanyalogin()  # 重试
            return error_msg
        except requests.exceptions.RequestException as e:
            error_msg = f"登录请求异常: {str(e)}"
            if not self.handle_error(error_msg, update_status=False):
                return self.fanyalogin()  # 重试
            return error_msg
        except Exception as e:
            error_msg = f"登录过程异常: {str(e)}"
            if not self.handle_error(error_msg, update_status=False):
                return self.fanyalogin()  # 重试
            return error_msg

    def kclist(self):
        self.retry_count = 0  # 重置重试计数
        self.KcList = list()

        # 平台ID 9004 特殊处理
        if self.cid == 9004:
            try:
                logger.info(
                    f"ID:{self.username},平台ID 9004，使用特殊处理方式获取作业信息"
                )
                # 直接构造课程信息
                course_info = {
                    "kcname": "作业任务",  # 默认课程名称
                    "courseid": self.courseid,
                    "clazzid": (
                        self.courseid.split("_")[0]
                        if "_" in self.courseid
                        else self.courseid
                    ),  # 从课程ID中提取班级ID
                    "cpi": (
                        self.courseid.split("_")[1] if "_" in self.courseid else ""
                    ),  # 从课程ID中提取cpi
                }
                self.KcList.append(course_info)
                logger.success(
                    f"ID:{self.username},成功构造作业课程信息: {course_info}"
                )
                return True
            except Exception as e:
                logger.error(f"ID:{self.username},构造作业课程信息失败: {str(e)}")
                traceback.print_exc()
                return False

        # 常规平台处理
        api = f"https://mooc1-api.chaoxing.com/mycourse/backclazzdata?view=json&mcode="
        try:
            logger.info(f"ID:{self.username},正在获取课程列表...")
            res = self.session.get(api, headers=UA().APP, timeout=30).json()
            logger.info(f"ID:{self.username},课程列表API返回: {res}")

            # 检查是否返回了验证码错误
            if isinstance(res, dict) and res.get("error") == "invalid_verify":
                logger.warning(
                    f"ID:{self.username},课程列表API返回验证码错误，尝试处理验证码"
                )
                # 尝试处理验证码
                if hasattr(self.session, "_StartSession__handle_anti_spider"):
                    self.session._StartSession__handle_anti_spider()
                    # 重新获取课程列表
                    logger.info(f"ID:{self.username},验证码处理后重新获取课程列表")
                    res = self.session.get(api, headers=UA().APP, timeout=30).json()
                    logger.info(f"ID:{self.username},重新获取课程列表API返回: {res}")

            if "channelList" not in res or not res["channelList"]:
                logger.error(f"ID:{self.username},未找到课程列表或课程列表为空")
                return False

            for i in res["channelList"]:
                try:
                    if (
                        "content" not in i
                        or "course" not in i["content"]
                        or "data" not in i["content"]["course"]
                        or not i["content"]["course"]["data"]
                    ):
                        continue

                    kcid = int(i["content"]["course"]["data"][0]["id"])
                    logger.info(
                        f"ID:{self.username},找到课程ID: {kcid}, 目标课程ID: {self.courseid}"
                    )

                    if int(self.courseid) == kcid:
                        course_info = {
                            "kcname": i["content"]["course"]["data"][0]["name"],
                            "courseid": self.courseid,
                            "clazzid": i["content"]["id"],
                            "cpi": i["content"]["cpi"],
                        }
                        self.KcList.append(course_info)
                        logger.success(
                            f"ID:{self.username},成功匹配课程: {course_info}"
                        )
                except Exception as e:
                    logger.error(f"ID:{self.username},处理课程信息时出错: {str(e)}")
                    continue

            if self.KcList:
                return True

            logger.error(f"ID:{self.username},未找到匹配的课程ID: {self.courseid}")
            return False
        except requests.exceptions.Timeout:
            error_msg = "获取课程列表超时，网络可能不稳定"
            if not self.handle_error(error_msg, update_status=False):
                return self.kclist()  # 重试
            return False
        except requests.exceptions.RequestException as e:
            error_msg = f"获取课程列表请求异常: {str(e)}"
            if not self.handle_error(error_msg, update_status=False):
                return self.kclist()  # 重试
            return False
        except Exception as e:
            logger.error(f"ID:{self.username},获取课程列表失败: {str(e)}")
            traceback.print_exc()
            error_msg = f"获取课程列表异常: {str(e)}"
            if not self.handle_error(error_msg, update_status=False):
                return self.kclist()  # 重试
            return False

    def studentcourse(self):
        self.retry_count = 0  # 重置重试计数
        self.listid = list()

        # 平台ID 9004 特殊处理 - 直接处理作业页面
        if self.cid == 9004:
            try:
                self.kcname = self.KcList[0]["kcname"]
                self.clazzid = self.KcList[0]["clazzid"]
                self.cpi = self.KcList[0]["cpi"]
                self.p = StartProces(self.session, self.KcList)

                # 检查是否包含分隔符"|"
                if "|" in self.courseid:
                    parts = self.courseid.split("|")

                    # 提前检查参数数量，减少不必要的日志输出
                    if len(parts) < 2:
                        logger.error(
                            f"ID:{self.username},竖线分隔的作业信息格式错误: {self.courseid}"
                        )
                        self.listid.append({"title": "格式错误", "id": "0"})
                        return

                    # 处理新格式: courseId|classId|cpi|workId|answerId|enc
                    if len(parts) >= 6:
                        course_id = parts[0]
                        class_id = parts[1]
                        cpi = parts[2]
                        work_id = parts[3]
                        answer_id = parts[4]
                        enc = parts[5]

                        # 验证必要参数
                        if not course_id or not work_id:
                            logger.error(
                                f"ID:{self.username},缺少必要参数: courseId={course_id}, workId={work_id}"
                            )
                            self.listid.append({"title": "参数缺失", "id": "0"})
                            return

                        # 更新课程信息
                        self.courseid = course_id
                        self.clazzid = class_id
                        self.cpi = cpi

                        # 更新KcList中的信息
                        self.KcList[0]["courseid"] = course_id
                        self.KcList[0]["clazzid"] = class_id
                        self.KcList[0]["cpi"] = cpi

                        # 构造作业信息
                        assignment_info = {
                            "workId": work_id,
                            "answerId": answer_id,
                            "enc": enc,
                            "knowledgeId": "0",
                            "courseId": course_id,
                            "classId": class_id,
                            "cpi": cpi,
                        }

                        # 构造完整作业链接
                        link = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId={course_id}&classId={class_id}&cpi={cpi}&workId={work_id}&answerId={answer_id}"
                        if enc:
                            link += f"&enc={enc}"

                        assignment_info["link"] = link

                        # 检查是否有standardEnc参数
                        if len(parts) > 6:
                            assignment_info["standardEnc"] = parts[6]
                            link += f"&standardEnc={parts[6]}"
                            assignment_info["link"] = link

                        # 添加到任务列表
                        self.listid.append(
                            {
                                "title": f"作业任务_{work_id}",
                                "id": "0",
                                "assignment_info": assignment_info,
                            }
                        )
                        return

                    # 处理课程ID|作业ID格式 (新格式)
                    if len(parts) == 2:
                        course_id = parts[0]
                        work_id = parts[1]

                        # 验证必要参数
                        if not course_id or not work_id:
                            logger.error(
                                f"ID:{self.username},缺少必要参数: courseId={course_id}, workId={work_id}"
                            )
                            self.listid.append({"title": "参数缺失", "id": "0"})
                            return

                        # 更新课程ID
                        self.courseid = course_id
                        self.KcList[0]["courseid"] = course_id

                        # 构造基本作业链接和信息
                        assignment_info = {
                            "workId": work_id,
                            "answerId": "0",  # 默认值
                            "enc": "",  # 默认值
                            "knowledgeId": "0",  # 默认值
                            "courseId": course_id,
                            "classId": self.clazzid,
                            "cpi": self.cpi,
                        }

                        # 构造基本作业链接
                        link = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId={course_id}&workId={work_id}"
                        assignment_info["link"] = link

                        # 输出警告日志
                        logger.warning(
                            f"ID:{self.username},作业任务_{work_id} 参数不足，需要完整6个参数"
                        )

                        # 立即更新进度状态为参数不足，确保状态为异常且进度为0%
                        p, r = self.p.get_platform_9004_progress(
                            homework_title=f"作业任务_{work_id}",
                            status="参数不足",
                            current_step=0,
                            total_steps=1,
                        )
                        self.pool.update_order(
                            f"update qingka_wangke_order set status = '异常', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                        )
                        logger.success(f"ID:{self.username},进度:{p},详情:{r}")

                        # 添加到任务列表，但标记为参数不足
                        self.listid.append(
                            {
                                "title": f"作业任务_{work_id}",
                                "id": "0",
                                "assignment_info": assignment_info,
                                "params_insufficient": True,  # 标记参数不足
                            }
                        )
                        return

                    # 处理原有格式: courseId|classId|cpi|... (旧格式)
                    elif len(parts) >= 3:
                        # 更新课程信息
                        self.courseid = parts[0]
                        self.clazzid = parts[1]
                        self.cpi = parts[2]

                        # 验证必要参数
                        if not self.courseid:
                            logger.error(
                                f"ID:{self.username},缺少必要参数: courseId={self.courseid}"
                            )
                            self.listid.append({"title": "参数缺失", "id": "0"})
                            return

                        # 更新KcList中的信息
                        self.KcList[0]["courseid"] = self.courseid
                        self.KcList[0]["clazzid"] = self.clazzid
                        self.KcList[0]["cpi"] = self.cpi

                        # 检查是否有完整的作业参数
                        if len(parts) >= 6:
                            # 格式: courseId|classId|cpi|workId|answerId|enc
                            workId = parts[3]
                            answerId = parts[4]
                            enc = parts[5]
                            knowledgeId = parts[6] if len(parts) > 6 else "0"

                            # 验证workId
                            if not workId:
                                logger.error(
                                    f"ID:{self.username},缺少必要参数: workId为空"
                                )
                                self.listid.append({"title": "参数缺失", "id": "0"})
                                return

                            assignment_info = {
                                "workId": workId,
                                "answerId": answerId,
                                "enc": enc,
                                "knowledgeId": knowledgeId,
                                "courseId": self.courseid,
                                "classId": self.clazzid,
                                "cpi": self.cpi,
                            }

                            # 构造完整作业链接
                            link = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId={self.courseid}&classId={self.clazzid}&cpi={self.cpi}&workId={workId}&answerId={answerId}"
                            if enc:
                                link += f"&enc={enc}"

                            # 检查是否有standardEnc参数
                            if len(parts) > 7:
                                assignment_info["standardEnc"] = parts[7]
                                link += f"&standardEnc={parts[7]}"

                            assignment_info["link"] = link

                            # 添加到任务列表
                            self.listid.append(
                                {
                                    "title": f"作业任务_{workId}",
                                    "id": knowledgeId,
                                    "assignment_info": assignment_info,
                                }
                            )
                        else:
                            # 3-5个参数但没有完整作业参数，标记为参数不足
                            logger.error(
                                f"ID:{self.username},竖线分隔的作业信息参数不足: {self.courseid}"
                            )
                            self.listid.append({"title": "参数不足", "id": "0"})
                        logger.error(
                            f"ID:{self.username},竖线分隔的作业信息格式错误: {self.courseid}"
                        )
                        self.listid.append({"title": "格式错误", "id": "0"})
                else:
                    # 处理下划线分隔的格式
                    parts = self.courseid.split("_")

                    if len(parts) >= 3:
                        # 根据parts的长度判断格式类型
                        if len(parts) >= 6:  # 格式2
                            workId = parts[3]
                            answerId = parts[4]
                            enc = parts[5]
                            knowledgeId = "0"  # 默认值

                            # 验证workId
                            if not workId:
                                logger.error(
                                    f"ID:{self.username},缺少必要参数: workId为空"
                                )
                                self.listid.append({"title": "参数缺失", "id": "0"})
                                return

                            # 格式2需要更新课程信息
                            self.courseid = parts[0]
                            self.clazzid = parts[1]
                            self.cpi = parts[2]

                            # 更新KcList中的信息
                            self.KcList[0]["courseid"] = self.courseid
                            self.KcList[0]["clazzid"] = self.clazzid
                            self.KcList[0]["cpi"] = self.cpi
                        else:  # 格式1
                            workId = parts[0]
                            answerId = parts[1]
                            enc = parts[2]
                            knowledgeId = parts[3] if len(parts) > 3 else "0"

                            # 验证workId
                            if not workId:
                                logger.error(
                                    f"ID:{self.username},缺少必要参数: workId为空"
                                )
                                self.listid.append({"title": "参数缺失", "id": "0"})
                                return

                        assignment_info = {
                            "workId": workId,
                            "answerId": answerId,
                            "enc": enc,
                            "knowledgeId": knowledgeId,
                            "courseId": self.courseid,
                            "classId": self.clazzid,
                            "cpi": self.cpi,
                        }

                        # 构造完整作业链接
                        link = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId={self.courseid}&classId={self.clazzid}&cpi={self.cpi}&workId={workId}&answerId={answerId}"
                        if enc:
                            link += f"&enc={enc}"

                        assignment_info["link"] = link

                        # 添加到任务列表
                        self.listid.append(
                            {
                                "title": f"作业任务_{workId}",
                                "id": knowledgeId,
                                "assignment_info": assignment_info,
                            }
                        )
                    else:
                        # 单独的courseId，构造基本作业链接
                        # 验证courseId
                        if not self.courseid:
                            logger.error(
                                f"ID:{self.username},缺少必要参数: courseId为空"
                            )
                            self.listid.append({"title": "参数缺失", "id": "0"})
                            return

                        # 输出警告日志
                        logger.warning(
                            f"ID:{self.username},作业列表 参数不足，需要完整6个参数"
                        )

                        # 立即更新进度状态为参数不足，确保状态为异常且进度为0%
                        p, r = self.p.get_platform_9004_progress(
                            homework_title="作业列表",
                            status="参数不足",
                            current_step=0,
                            total_steps=1,
                        )
                        self.pool.update_order(
                            f"update qingka_wangke_order set status = '异常', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                        )
                        logger.success(f"ID:{self.username},进度:{p},详情:{r}")

                        # 构造基本作业链接
                        link = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/list?courseId={self.courseid}"

                        # 添加到任务列表，并标记为参数不足
                        self.listid.append(
                            {
                                "title": "作业列表",
                                "id": "0",
                                "assignment_info": {
                                    "link": link,
                                    "courseId": self.courseid,
                                },
                                "params_insufficient": True,  # 标记参数不足
                            }
                        )
                return
            except Exception as e:
                logger.error(f"ID:{self.username},处理作业信息异常: {str(e)}")
                traceback.print_exc()
                self.listid.append({"title": "处理异常", "id": "0"})
                return

        # 常规课程处理
        try:
            self.kcname = self.KcList[0]["kcname"]
            self.clazzid = self.KcList[0]["clazzid"]
            self.cpi = self.KcList[0]["cpi"]
            self.p = StartProces(self.session, self.KcList)

            r = self.session.get(
                "https://mooc2-ans.chaoxing.com/mooc2-ans/mycourse/studentcourse",
                params={
                    "courseid": self.courseid,
                    "clazzid": self.clazzid,
                    "cpi": self.cpi,
                    "ut": "s",
                    "t": t(),
                },
                timeout=30,
            )

            soup = BeautifulSoup(r.text, "html.parser")

            if "很抱歉，您所浏览的页面不存在！" in r.text:
                self.listid.append({"title": "页面不存在", "id": "0"})
                logger.success(f"ID:{self.username},未开课")
                return

            for name in soup.find_all("div", {"class": "chapter_unit"}):
                for i in name.find_all("li"):
                    status = i.find("span", {"class": "bntHoverTips"})
                    if status:
                        status = status.text.strip()
                    else:
                        status = None

                    # 检查是否有"已关闭"状态
                    if status == "已关闭":
                        logger.warning(
                            f"ID:{self.username},发现已关闭的章节，课程可能已结束"
                        )
                        # 检查是否所有章节都已关闭
                        all_closed = True
                        for other_li in name.find_all("li"):
                            other_status = other_li.find(
                                "span", {"class": "bntHoverTips"}
                            )
                            if (
                                other_status
                                and other_status.text.strip() != "已关闭"
                                and other_status.text.strip() != "已完成"
                            ):
                                all_closed = False
                                break

                        if all_closed:
                            logger.success(
                                f"ID:{self.username},所有章节都已关闭，课程已结束"
                            )
                            self.listid.append({"title": "课程已关闭", "id": "0"})
                            # 更新数据库状态
                            formatted_time = datetime.datetime.now().strftime(
                                "%Y-%m-%d %H:%M:%S"
                            )
                            try:
                                p, r = self.p.get_xxt_jxz()
                                r = f"课程已关闭，无法继续学习 | 更新:{formatted_time}"
                                self.pool.update_order(
                                    f"update qingka_wangke_order set status = '已关闭', process = '100%',remarks = '{r}' where oid = '{self.oid}'"
                                )
                                logger.success(f"ID:{self.username},进度:{p},详情:{r}")
                            except Exception as e:
                                logger.error(
                                    f"ID:{self.username},更新课程已关闭状态失败: {str(e)}"
                                )
                            return

                    if status is None:
                        pass
                    elif status != "已完成" and status != "已关闭":
                        title = i.find("a", {"class": "clicktitle"}).text.strip()
                        try:
                            ListId = (
                                i.find("div", {"class": "chapter_item"})
                                .get("id")
                                .replace("cur", "")
                            )
                            self.listid.append({"title": title, "id": ListId})
                        except Exception as e:
                            logger.warning(
                                f"ID:{self.username},获取章节ID失败: {str(e)}"
                            )

            logger.success(
                f"ID:{self.username},课件获取完成，总共获取到{len(self.listid)}个未完成章节"
            )

        except requests.exceptions.Timeout:
            error_msg = "获取课程章节超时，网络可能不稳定"
            if not self.handle_error(error_msg, update_status=False):
                return self.studentcourse()  # 重试
            return
        except requests.exceptions.RequestException as e:
            error_msg = f"获取课程章节请求异常: {str(e)}"
            if not self.handle_error(error_msg, update_status=False):
                return self.studentcourse()  # 重试
            return
        except Exception as e:
            logger.error(f"ID:{self.username},获取课程章节失败: {str(e)}")
            traceback.print_exc()
            error_msg = f"获取课程章节异常: {str(e)}"
            if not self.handle_error(error_msg, update_status=False):
                return self.studentcourse()  # 重试
            return

    def studentstudyAjax(self):
        self.retry_count = 0  # 重置重试计数
        self.cishu = None
        try:
            r = self.session.get(
                f"https://mooc1.chaoxing.com/mooc-ans/mycourse/studentstudyAjax",
                params={
                    "courseId": self.courseid,
                    "clazzid": self.clazzid,
                    "chapterId": self.chapterId,
                    "cpi": self.cpi,
                    "verificationcode": "false",
                    "mooc2": "1",
                    "microTopicId": "0",
                },
                timeout=30,
            ).text
            res = BeautifulSoup(r, "html.parser")
            prev_ul = res.find("ul", {"class": "prev_ul clearfix"})
            if prev_ul:
                self.noun = len(prev_ul.find_all("li"))
            else:
                self.noun = 1
                logger.warning(f"ID:{self.username},未找到章节导航，默认设置为1")

            try:
                self.cishu = (
                    parsel.Selector(r)
                    .xpath("//script[@type='text/javascript']/@src")
                    .get()
                )
            except:
                pass
        except requests.exceptions.Timeout:
            error_msg = "获取章节页面超时，网络可能不稳定"
            if not self.handle_error(error_msg, update_status=False):
                return self.studentstudyAjax()  # 重试
            self.noun = 1  # 默认值
        except requests.exceptions.RequestException as e:
            error_msg = f"获取章节页面请求异常: {str(e)}"
            if not self.handle_error(error_msg, update_status=False):
                return self.studentstudyAjax()  # 重试
            self.noun = 1  # 默认值
        except Exception as e:
            logger.error(f"ID:{self.username},获取章节页面失败: {str(e)}")
            self.noun = 1  # 默认值

    def studentstudy(self):
        if not self.is_running:
            logger.warning(f"ID:{self.username},任务已被终止")
            return

        self.retry_count = 0  # 重置重试计数

        # 默认初始化status变量，确保在任何情况下都有值
        status = "已完成"
        total_chapters = 0

        if self.listid:
            value = 0
            total_chapters = len(self.listid)
            completed_chapters = 0

            for i in self.listid:
                if not self.is_running:
                    break

                title = i["title"]
                self.sleep()

                try:
                    # 更新进度
                    completed_chapters += 1
                    progress_percent = int((completed_chapters / total_chapters) * 100)
                    formatted_time = datetime.datetime.now().strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )

                    # 平台ID 9004 特殊处理 - 检查参数是否不足
                    if self.cid == 9004 and "assignment_info" in i:
                        # 检查参数是否不足
                        if "params_insufficient" in i and i["params_insufficient"]:
                            # 参数不足，直接更新状态为异常，强制进度为0%
                            logger.warning(
                                f"ID:{self.username},作业 {title} 参数不足，跳过处理"
                            )
                            p, r = self.p.get_platform_9004_progress(
                                homework_title=title,
                                status="异常",
                            )
                            self.pool.update_order(
                                f"update qingka_wangke_order set status = '异常', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                            )
                            logger.success(f"ID:{self.username},进度:{p},详情:{r}")
                            continue

                        # 检查标题是否表示错误状态
                        if title in [
                            "参数缺失",
                            "格式错误",
                            "处理异常",
                            "未找到作业",
                            "未找到有效作业",
                            "解析作业列表异常",
                            "参数不足",
                        ]:
                            # 更新进度为异常状态
                            p, r = self.p.get_platform_9004_progress(
                                homework_title=title,
                                status="异常",
                            )
                            self.pool.update_order(
                                f"update qingka_wangke_order set status = '异常', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                            )
                            logger.success(f"ID:{self.username},进度:{p},详情:{r}")
                            continue

                    # 正常更新进度
                    if self.cid == 9004:
                        # 检查是否为参数不足的情况
                        if "params_insufficient" in i and i["params_insufficient"]:
                            # 参数不足的情况已在前面处理，这里跳过
                            continue

                        # 获取作业真实标题
                        display_title = title
                        if re.match(r"^作业任务_\d+$", title):
                            work_id = title.split("_")[1]
                            if work_id in self.real_titles:
                                display_title = self.real_titles[work_id]

                        p, r = self.p.get_platform_9004_progress(
                            current_step=completed_chapters,
                            total_steps=total_chapters,
                            homework_title=display_title,
                            status="进行中",
                        )
                    else:
                        try:
                            # 尝试获取进度，如果失败则使用默认值
                            p, r = self.p.get_xxt_jxz()
                            # 即使get_xxt_jxz成功，也使用自己计算的进度百分比，确保进度显示正确
                            p = f"{progress_percent}%"
                            # 添加当前执行的章节信息
                            r += f" | 实时执行：{title} ({completed_chapters}/{total_chapters}) | 更新:{formatted_time}"
                        except Exception as e:
                            # 如果获取进度失败，使用自己计算的进度
                            logger.warning(
                                f"ID:{self.username},获取进度失败: {str(e)}，使用默认进度"
                            )
                            p = f"{progress_percent}%"
                            r = f"课程任务:?/? | 章节测验:未知 | 实时执行：{title} ({completed_chapters}/{total_chapters}) | 更新:{formatted_time}"

                    self.pool.update_order(
                        f"update qingka_wangke_order set process = '{p}',remarks = '{r}' where oid = '{self.oid}'"
                    )
                    logger.success(f"ID:{self.username},进度:{p},详情:{r}")

                    value += 1
                    self.chapterId = i["id"]

                    # 平台ID 9004 特殊处理 - 直接处理作业任务
                    if self.cid == 9004 and "assignment_info" in i:
                        try:
                            # 获取作业URL
                            assignment_url = i.get("assignment_info", {}).get("url", "")
                            if not assignment_url:
                                # 如果没有直接URL，尝试从link参数获取
                                assignment_url = i.get("assignment_info", {}).get(
                                    "link", ""
                                )

                            if not assignment_url:
                                # 如果仍然没有URL，尝试构建
                                if "workId" in i.get("assignment_info", {}):
                                    workid = i["assignment_info"]["workId"]
                                    if not workid:
                                        # 更新进度为异常状态
                                        p, r = self.p.get_platform_9004_progress(
                                            current_step=completed_chapters,
                                            total_steps=total_chapters,
                                            homework_title=title,
                                            status="异常",
                                        )
                                        self.pool.update_order(
                                            f"update qingka_wangke_order set status = '异常', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                                        )
                                        continue

                                    assignment_url = f"https://mooc1.chaoxing.com/mooc-ans/mooc2/work/task?courseId={self.courseid}&classId={self.clazzid}&cpi={self.cpi}&workId={workid}"

                            if not assignment_url:
                                # 更新进度为异常状态
                                try:
                                    # 获取作业真实标题
                                    display_title = title
                                    if re.match(r"^作业任务_\d+$", title):
                                        work_id = title.split("_")[1]
                                        if work_id in self.real_titles:
                                            display_title = self.real_titles[work_id]

                                    p, r = self.p.get_platform_9004_progress(
                                        current_step=completed_chapters,
                                        total_steps=total_chapters,
                                        homework_title=display_title,
                                        status="作业URL无效",
                                    )
                                    self.pool.update_order(
                                        f"update qingka_wangke_order set status = '异常', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                                    )
                                except Exception as e:
                                    logger.error(
                                        f"ID:{self.username},更新作业URL无效状态失败: {str(e)}"
                                    )
                                continue

                            # 检查作业URL有效性
                            if not self.check_homework_url(assignment_url, title):
                                # 更新进度为异常状态
                                try:
                                    # 获取作业真实标题
                                    display_title = title
                                    if re.match(r"^作业任务_\d+$", title):
                                        work_id = title.split("_")[1]
                                        if work_id in self.real_titles:
                                            display_title = self.real_titles[work_id]

                                    p, r = self.p.get_platform_9004_progress(
                                        current_step=completed_chapters,
                                        total_steps=total_chapters,
                                        homework_title=display_title,
                                        status="作业URL无效",
                                    )
                                    self.pool.update_order(
                                        f"update qingka_wangke_order set status = '异常', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                                    )
                                except Exception as e:
                                    logger.error(
                                        f"ID:{self.username},更新作业URL无效状态失败: {str(e)}"
                                    )
                                continue

                            # 直接使用传统方法处理作业，跳过HomeworkAI模块
                            result = self._fallback_to_traditional_homework(i, title)

                        except Exception as e:
                            logger.error(
                                f"ID:{self.username},处理作业任务异常: {str(e)}"
                            )
                            traceback.print_exc()

                            # 更新进度为异常状态
                            # 获取作业真实标题
                            display_title = title
                            if re.match(r"^作业任务_\d+$", title):
                                work_id = title.split("_")[1]
                                if work_id in self.real_titles:
                                    display_title = self.real_titles[work_id]

                            p, r = self.p.get_platform_9004_progress(
                                current_step=completed_chapters,
                                total_steps=total_chapters,
                                homework_title=display_title,
                                status="处理异常",
                            )
                            self.pool.update_order(
                                f"update qingka_wangke_order set status = '异常', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                            )

                        # 等待一段时间后处理下一个任务
                        wait_time = random.randint(5, 10)
                        logger.info(
                            f"ID:{self.username},作业任务 {title} 完成，等待 {wait_time} 秒后处理下一任务"
                        )
                        time.sleep(wait_time)
                        continue

                    # 常规课程处理
                    self.studentstudyAjax()

                    for num in range(self.noun):
                        if not self.is_running:
                            break

                        try:
                            if self.cishu is not None:
                                self.session.get(self.cishu, timeout=30)  # 学习次数

                            r = self.session.get(
                                "https://mooc1.chaoxing.com/mooc-ans/knowledge/cards",
                                params={
                                    "clazzid": self.clazzid,
                                    "courseid": self.courseid,
                                    "knowledgeid": self.chapterId,
                                    "num": num,
                                    "ut": "s",
                                    "cpi": self.cpi,
                                    "v": "20160407-3",
                                    "mooc2": "1",
                                    "isMicroCourse": "false",
                                },
                                timeout=30,
                            ).text

                            html = str(
                                BeautifulSoup(r, features="lxml")
                                .find("body")
                                .find_all("script")[0]
                            )
                            pattern = re.compile(r"mArg = ({[\s\S]*)}catch")
                            datas = re.findall(pattern, html)[0]
                            attachment = json.loads(datas.strip()[:-1])["attachments"]
                            defaults = json.loads(datas.strip()[:-1])["defaults"]
                            tk = Task(
                                self.session,
                                self.KcList,
                                attachment,
                                defaults,
                                self.chapterId,
                                self.username,
                            )
                            tk.task()
                        except requests.exceptions.Timeout:
                            logger.warning(
                                f"ID:{self.username},处理任务点超时，跳过当前任务点"
                            )
                            continue
                        except requests.exceptions.RequestException as e:
                            logger.warning(
                                f"ID:{self.username},处理任务点请求异常: {str(e)}，跳过当前任务点"
                            )
                            continue
                        except Exception as e:
                            logger.warning(
                                f"ID:{self.username},处理任务点异常: {str(e)}，跳过当前任务点"
                            )
                            continue

                        time.sleep(random.randint(2, 5))

                    # 章节之间随机等待
                    wait_time = random.randint(60, 90)
                    logger.info(
                        f"ID:{self.username},章节 {title} 完成，等待 {wait_time} 秒后处理下一章节"
                    )
                    time.sleep(wait_time)

                except Exception as e:
                    logger.error(
                        f"ID:{self.username},处理章节 {title} 时出错: {str(e)}"
                    )
                    traceback.print_exc()
                    continue  # 继续处理下一章节

            # 所有章节处理完毕，检查考试状态
            try:
                # 默认初始化status变量，确保在任何情况下都有值
                status = "已完成"

                if self.cid == 9004:
                    # 平台ID 9004 特殊处理
                    # 检查是否有参数不足的任务
                    has_insufficient_params = any(
                        "params_insufficient" in item and item["params_insufficient"]
                        for item in self.listid
                        if isinstance(item, dict)
                    )

                    # 检查是否有作业已提交过
                    has_submitted_homework = any(
                        isinstance(item, dict)
                        and "title" in item
                        and "已提交过" in str(item.get("title"))
                        for item in self.listid
                    ) or any(
                        hasattr(self, "real_titles") and "已提交过" in str(value)
                        for value in self.real_titles.values()
                    )

                    if has_insufficient_params:
                        # 如果有参数不足的任务，保持状态为异常
                        status = "异常"
                    else:
                        # 检查listid中是否有标题表示错误状态
                        has_error_title = any(
                            isinstance(item, dict)
                            and item.get("title")
                            in [
                                "参数缺失",
                                "格式错误",
                                "处理异常",
                                "未找到作业",
                                "未找到有效作业",
                                "解析作业列表异常",
                                "参数不足",
                            ]
                            for item in self.listid
                        )

                        if has_error_title:
                            status = "异常"
                        elif has_submitted_homework:
                            # 如果有作业已提交过，更新状态为已提交过
                            status = "已提交过"
                        else:
                            status = "已完成"
                else:
                    ks_s = self.p.get_xxt_wc()
                    if ks_s is True:
                        status = "已完成"
                    else:
                        em = EXAM(self.session, self.username, self.KcList, open=1)
                        st = em.get_data()
                        status = "待考试" if st is False else "已完成"
                        if self.cid == 9001:
                            status = "已完成"
            except Exception as e:
                logger.error(f"ID:{self.username},检查考试状态时出错: {str(e)}")
                # 确保status变量在异常情况下也有值
                status = "已完成"  # 默认值

                # 对于平台ID 9004，如果出错，检查是否有参数不足的任务
                if self.cid == 9004:
                    try:
                        has_insufficient_params = any(
                            "params_insufficient" in item
                            and item["params_insufficient"]
                            for item in self.listid
                            if isinstance(item, dict)
                        )

                        # 检查listid中是否有标题表示错误状态
                        has_error_title = any(
                            isinstance(item, dict)
                            and item.get("title")
                            in [
                                "参数缺失",
                                "格式错误",
                                "处理异常",
                                "未找到作业",
                                "未找到有效作业",
                                "解析作业列表异常",
                                "参数不足",
                            ]
                            for item in self.listid
                        )

                        # 检查是否有作业已提交过
                        has_submitted_homework = any(
                            isinstance(item, dict)
                            and "title" in item
                            and "已提交过" in str(item.get("title"))
                            for item in self.listid
                        ) or any(
                            hasattr(self, "real_titles") and "已提交过" in str(value)
                            for value in self.real_titles.values()
                        )

                        if has_insufficient_params or has_error_title:
                            status = "异常"
                        elif has_submitted_homework:
                            status = "已提交过"
                        else:
                            status = "已完成"
                    except Exception as inner_e:
                        logger.error(
                            f"ID:{self.username},检查参数不足状态时出错: {str(inner_e)}"
                        )
                        status = "已完成"  # 内部异常时的默认值

            # 处理空listid的情况
            if not self.listid:
                if self.cid == 9004:
                    # 平台ID 9004 特殊处理 - 空列表视为异常
                    status = "异常"
                else:
                    # 检查是否是"页面不存在"情况
                    if (
                        isinstance(self.listid, list)
                        and len(self.listid) > 0
                        and isinstance(self.listid[0], dict)
                    ):
                        if self.listid[0].get("title") == "页面不存在":
                            status = "未开课"
                        elif self.listid[0].get("title") == "课程已关闭":
                            status = "已关闭"
                        elif self.listid[0].get("title") in [
                            "格式错误",
                            "处理异常",
                            "未找到作业",
                            "未找到有效作业",
                            "解析作业列表异常",
                            "参数不足",
                        ]:
                            status = self.listid[0].get("title")
                        else:
                            # 没有章节任务，但需要检查考试状态
                            logger.info(f"ID:{self.username},没有未完成章节，检查考试状态")
                            try:
                                ks_s = self.p.get_xxt_wc()
                                if ks_s is True:
                                    status = "已完成"
                                else:
                                    em = EXAM(self.session, self.username, self.KcList, open=1)
                                    st = em.get_data()
                                    status = "待考试" if st is False else "已完成"
                                    if self.cid == 9001:
                                        status = "已完成"
                            except Exception as e:
                                logger.error(f"ID:{self.username},检查考试状态时出错: {str(e)}")
                                status = "已完成"  # 异常时默认为已完成
                    else:
                        # 空列表，但需要检查考试状态
                        logger.info(f"ID:{self.username},课程章节列表为空，检查考试状态")
                        try:
                            ks_s = self.p.get_xxt_wc()
                            if ks_s is True:
                                status = "已完成"
                            else:
                                em = EXAM(self.session, self.username, self.KcList, open=1)
                                st = em.get_data()
                                status = "待考试" if st is False else "已完成"
                                if self.cid == 9001:
                                    status = "已完成"
                        except Exception as e:
                            logger.error(f"ID:{self.username},检查考试状态时出错: {str(e)}")
                            status = "已完成"  # 异常时默认为已完成

        # 更新最终状态
        try:
            formatted_time = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 平台ID 9004 特殊处理 - 使用专用的进度计算方法
            if self.cid == 9004:
                # 检查是否有参数不足的任务
                has_insufficient_params = any(
                    "params_insufficient" in item and item["params_insufficient"]
                    for item in self.listid
                    if isinstance(item, dict)
                )

                # 获取最后处理的作业标题
                last_title = ""
                has_submitted_title = False

                # 首先检查real_titles中是否有带"已提交过"标记的标题
                if hasattr(self, "real_titles") and self.real_titles:
                    for work_id, title in self.real_titles.items():
                        if "已提交过" in title:
                            last_title = title
                            has_submitted_title = True
                            break

                # 如果没有找到带"已提交过"标记的标题，则使用常规方法获取
                if not has_submitted_title:
                    for item in self.listid:
                        if isinstance(item, dict) and "title" in item:
                            last_title = item["title"]
                            # 如果是作业任务_数字格式，尝试从real_title记录中获取真实标题
                            if re.match(r"^作业任务_\d+$", last_title):
                                work_id = last_title.split("_")[1]
                                # 检查是否有对应的真实标题记录
                                if (
                                    hasattr(self, "real_titles")
                                    and work_id in self.real_titles
                                ):
                                    last_title = self.real_titles[work_id]
                            break

                if not last_title:
                    last_title = "作业任务"

                # 如果状态为"已提交过"，确保标题中包含"已提交过"标记
                if status == "已提交过" and "已提交过" not in last_title:
                    last_title = f"{last_title} (已提交过)"

                if has_insufficient_params or status == "异常":
                    # 如果有参数不足的任务或状态为异常，强制进度为0%
                    p, r = self.p.get_platform_9004_progress(
                        current_step=0,
                        total_steps=total_chapters,
                        homework_title=last_title,
                        status="参数不足" if has_insufficient_params else status,
                    )
                    # 强制进度为0%
                    p = "0%"

                    # 使用明确的进度值0%更新数据库
                    self.pool.update_order(
                        f"update qingka_wangke_order set status = '{status}',process='0%',remarks='{r}' where oid = '{self.oid}'"
                    )
                    return  # 提前返回，不执行后面的通用更新语句
                else:
                    p, r = self.p.get_platform_9004_progress(
                        current_step=total_chapters,
                        total_steps=total_chapters,
                        homework_title=last_title,
                        status=status,
                    )
            else:
                try:
                    # 尝试获取进度，如果失败则使用默认值
                    p, r = self.p.get_xxt_jxz()

                    # 如果状态为"已关闭"，更新备注信息
                    if status == "已关闭":
                        formatted_time = datetime.datetime.now().strftime(
                            "%Y-%m-%d %H:%M:%S"
                        )
                        r = f"课程已关闭，无法继续学习 | 更新:{formatted_time}"
                        p = "100%"  # 课程已关闭时，进度设为100%
                    else:
                        r += f" | 实时执行：课程已完成【退出执行】 | 更新:{datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
                except Exception as e:
                    # 如果获取进度失败，使用默认值
                    logger.warning(
                        f"ID:{self.username},获取最终进度失败: {str(e)}，使用默认进度"
                    )
                    formatted_time = datetime.datetime.now().strftime(
                        "%Y-%m-%d %H:%M:%S"
                    )
                    if status == "已关闭":
                        p = "100%"  # 课程已关闭时，进度设为100%
                        r = f"课程已关闭，无法继续学习 | 更新:{formatted_time}"
                    else:
                        p = "100%"  # 最终状态默认为100%
                        r = f"课程任务:?/? | 章节测验:未知 | 实时执行：课程已完成【退出执行】 | 更新:{formatted_time}"

            self.pool.update_order(
                f"update qingka_wangke_order set status = '{status}',process='{p}',remarks='{r}' where oid = '{self.oid}'"
            )
        except Exception as e:
            logger.error(f"ID:{self.username},更新最终状态时出错: {str(e)}")
            # 尝试一次简单更新
            self.pool.update_order(
                f"update qingka_wangke_order set status = '{status}' where oid = '{self.oid}'"
            )

        # 释放账号锁
        ThreadManager.account_unlock(self.username, self.cid)

    def sleep(self):
        """休眠方法，可以被外部中断"""
        xun = 0
        try:
            while self.is_running:
                current_time = datetime.datetime.now().time()
                # 晚上23点到第二天早上7点之间不执行任何操作，cid=9003和cid=9004不受此限制
                if (
                    (
                        current_time >= datetime.time(23, 10)
                        or current_time < datetime.time(7, 30)
                    )
                    and self.cid != 9003
                    and self.cid != 9004
                ):
                    xun += 1
                    if xun == 1:
                        # 首次进入夜间模式
                        formatted_time = datetime.datetime.now().strftime(
                            "%Y-%m-%d %H:%M:%S"
                        )
                        try:
                            p, r = self.p.get_xxt_jxz()
                            r += f" | 实时执行：人工进行已下线，等待次日7：30继续进行 | 更新:{formatted_time}"
                        except Exception as e:
                            logger.warning(
                                f"ID:{self.username},夜间模式获取进度失败: {str(e)}，使用默认进度"
                            )
                            p = "10%"  # 夜间模式默认进度
                            r = f"课程任务:?/? | 章节测验:未知 | 实时执行：人工进行已下线，等待次日7：30继续进行 | 更新:{formatted_time}"

                        try:
                            self.pool.update_order(
                                f"update qingka_wangke_order set status = '停止中',process='{p}',remarks='{r}' where oid = '{self.oid}'"
                            )
                        except Exception as e:
                            logger.error(
                                f"ID:{self.username},更新夜间状态失败: {str(e)}"
                            )
                            try:
                                self.pool.update_order(
                                    f"update qingka_wangke_order set status = '停止中' where oid = '{self.oid}'"
                                )
                            except:
                                pass

                    # 每15分钟检查一次，分段检查让休眠可以被中断
                    for _ in range(6):  # 6 * 150秒 = 15分钟
                        if not self.is_running:
                            break
                        time.sleep(150)

                    # 如果已经被终止，则退出休眠
                    if not self.is_running:
                        break

                    continue

                # 不在夜间或特殊平台，更新状态为进行中并退出休眠
                try:
                    self.pool.update_order(
                        f"update qingka_wangke_order set status = '进行中' where oid = '{self.oid}'"
                    )
                except Exception as e:
                    logger.error(f"ID:{self.username},更新运行状态失败: {str(e)}")

                break
        except Exception as e:
            logger.error(f"ID:{self.username},休眠方法异常: {str(e)}")
            # 如果休眠方法出错，直接继续执行
            try:
                self.pool.update_order(
                    f"update qingka_wangke_order set status = '进行中' where oid = '{self.oid}'"
                )
            except:
                pass

    def _fallback_to_traditional_homework(self, assignment_item, title):
        """使用传统方法处理作业任务"""
        try:
            # 导入AssignmentTask类
            from API.TaskDo import AssignmentTask

            # 获取作业信息
            assignment_info = assignment_item["assignment_info"]

            # 尝试获取真实作业标题
            real_title = title
            try:
                # 获取作业URL
                assignment_url = assignment_info.get("link", "")
                if assignment_url:
                    # 发送请求获取作业页面
                    r = self.session.get(
                        assignment_url,
                        headers={"User-Agent": UA().WEB},
                        allow_redirects=True,
                        timeout=10,
                    )
                    if r.status_code == 200:
                        # 使用BeautifulSoup解析页面
                        soup = BeautifulSoup(r.text, "html.parser")
                        # 尝试获取作业标题
                        mark_title = soup.select_one(".mark_title")
                        if mark_title:
                            real_title = mark_title.text.strip()
                            logger.info(
                                f"ID:{self.username},获取到真实作业标题: {real_title}"
                            )

                            # 更新real_titles映射
                            if "workId" in assignment_info:
                                work_id = assignment_info["workId"]
                                self.real_titles[work_id] = real_title
                        else:
                            # 尝试其他可能的标题元素
                            for title_selector in [
                                ".mark_title",
                                "h3.title",
                                ".titTxt",
                                "h1",
                                "h2.title",
                            ]:
                                title_elem = soup.select_one(title_selector)
                                if title_elem:
                                    real_title = title_elem.text.strip()
                                    logger.info(
                                        f"ID:{self.username},获取到真实作业标题: {real_title}"
                                    )

                                    # 更新real_titles映射
                                    if "workId" in assignment_info:
                                        work_id = assignment_info["workId"]
                                        self.real_titles[work_id] = real_title
                                    break

                        # 检查是否有正确答案标记，表示作业已提交过
                        correct_answers = soup.select("i.fontWeight.custom-style")
                        if correct_answers and any(
                            "正确答案" in elem.text for elem in correct_answers
                        ):
                            logger.success(
                                f"ID:{self.username},找到正确答案标记，该作业已提交过"
                            )

                            # 将作业标题更新为包含"已提交过"标记
                            if "已提交过" not in real_title:
                                real_title_with_mark = f"{real_title} (已提交过)"
                            else:
                                real_title_with_mark = real_title

                            # 更新real_titles映射，保存带有"已提交过"标记的标题
                            if "workId" in assignment_info:
                                work_id = assignment_info["workId"]
                                self.real_titles[work_id] = real_title_with_mark

                            # 更新进度信息
                            if self.cid == 9004:
                                p, r = self.p.get_platform_9004_progress(
                                    homework_title=real_title_with_mark,
                                    status="已提交过",
                                    current_step=1,
                                    total_steps=1,
                                )
                                self.pool.update_order(
                                    f"update qingka_wangke_order set process = '{p}',remarks = '{r}' where oid = '{self.oid}'"
                                )
                            return True
            except Exception as e:
                logger.debug(f"ID:{self.username},获取真实作业标题失败: {str(e)}")
                # 保持原有标题

            # 如果URL中包含courseId、classId、cpi参数，更新KcList中的信息
            try:
                if (
                    self.KcList[0]["courseid"] != self.courseid
                    or self.KcList[0]["clazzid"] != self.clazzid
                    or self.KcList[0]["cpi"] != self.cpi
                ):
                    self.KcList[0]["courseid"] = self.courseid
                    self.KcList[0]["clazzid"] = self.clazzid
                    self.KcList[0]["cpi"] = self.cpi
            except Exception as e:
                logger.debug(f"ID:{self.username},更新KcList信息失败: {str(e)}")

            # 平台ID 9004 特殊处理 - 更新处理中的进度
            if self.cid == 9004:
                p, r = self.p.get_platform_9004_progress(
                    homework_title=real_title, status="处理中"
                )
                self.pool.update_order(
                    f"update qingka_wangke_order set process = '{p}',remarks = '{r}' where oid = '{self.oid}'"
                )

            # 创建作业任务处理对象
            assignment_task = AssignmentTask(
                self.session,
                self.KcList,
                assignment_info,
                self.username,
                self.oid,  # 传递oid参数
            )

            # 为session添加cid属性，用于在AssignmentTask中识别平台ID
            if self.cid == 9004:
                self.session.cid = self.cid

            # 处理作业任务
            result = assignment_task.process()

            # 平台ID 9004 特殊处理 - 更新处理结果
            if self.cid == 9004:
                if result:
                    status = "已完成"
                    # 这里不需要清除session中的ai_status_info，它将被自动添加到进度信息中
                    p, r = self.p.get_platform_9004_progress(
                        homework_title=real_title, status=status
                    )
                    self.pool.update_order(
                        f"update qingka_wangke_order set process = '{p}',remarks = '{r}' where oid = '{self.oid}'"
                    )
                    logger.success(f"ID:{self.username},进度:{p},详情:{r}")
                else:
                    # 处理失败时设置为异常状态
                    status = "异常"
                    # 清除任何可能的AI状态信息，避免在错误状态下显示
                    if hasattr(self.session, "ai_status_info"):
                        delattr(self.session, "ai_status_info")
                    p, r = self.p.get_platform_9004_progress(
                        homework_title=real_title, status="处理异常"
                    )
                    self.pool.update_order(
                        f"update qingka_wangke_order set status = '{status}', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                    )

            if result:
                logger.success(
                    f"ID:{self.username},传统方法作业任务 {real_title} 处理成功"
                )
            else:
                logger.warning(
                    f"ID:{self.username},传统方法作业任务 {real_title} 处理可能未完全成功"
                )
            return result
        except Exception as e:
            logger.error(f"ID:{self.username},传统方法处理作业任务异常: {str(e)}")
            traceback.print_exc()

            # 平台ID 9004 特殊处理 - 更新处理异常
            if self.cid == 9004:
                status = "异常"
                # 清除任何可能的AI状态信息，避免在错误状态下显示
                if hasattr(self.session, "ai_status_info"):
                    delattr(self.session, "ai_status_info")
                p, r = self.p.get_platform_9004_progress(
                    homework_title=title, status="处理异常"
                )
                self.pool.update_order(
                    f"update qingka_wangke_order set status = '{status}', process = '0%',remarks = '{r}' where oid = '{self.oid}'"
                )
            return False

    def check_homework_url(self, url, title="作业"):
        """
        检查作业URL的有效性

        Args:
            url: 作业URL
            title: 作业标题，用于日志输出

        Returns:
            bool: URL是否有效
        """
        try:
            if not url:
                return False

            # 发送HEAD请求检查URL是否可访问
            r = self.session.get(
                url, headers={"User-Agent": UA().WEB}, allow_redirects=False, timeout=10
            )

            # 检查状态码
            if r.status_code == 200:
                return True
            elif r.status_code == 302 and "Location" in r.headers:
                # 处理重定向
                return True
            else:
                return False

        except requests.exceptions.Timeout:
            return False
        except requests.exceptions.RequestException:
            return False
        except Exception:
            return False


def order_get(pool):
    """使用线程池和队列获取并处理订单"""
    logger.info(f"订单处理线程启动，最大线程数: {MAX_WORKERS}")

    # 创建线程池
    with concurrent.futures.ThreadPoolExecutor(max_workers=MAX_WORKERS) as executor:
        futures = {}  # 存储提交的任务

        while running:
            try:
                # 移除已完成的任务
                done_oids = [oid for oid, future in futures.items() if future.done()]
                for oid in done_oids:
                    del futures[oid]

                # 检查当前活跃线程数
                active_count = ThreadManager.get_active_count()
                available_slots = MAX_WORKERS - active_count

                if available_slots > 0:
                    # 查询待处理订单
                    orders = pool.get_order(
                        f"SELECT * FROM qingka_wangke_order WHERE cid IN (9000,9001,9002,9003,9004) AND "
                        f"(status = '待处理' or status = '补刷中') ORDER BY oid LIMIT {available_slots}"
                    )

                    if orders:
                        logger.info(
                            f"获取到 {len(orders)} 个待处理订单，当前活跃线程数: {active_count}"
                        )

                        for order in orders:
                            try:
                                oid = order["oid"]
                                cid = order["cid"]
                                school = order["school"]
                                username = order["user"]
                                password = order["pass"]
                                kcid = order["kcid"]

                                # 检查该账号是否已有任务在运行
                                if not ThreadManager.account_lock(username, cid):
                                    logger.info(f"账号 {username} 已有任务在运行，跳过")
                                    pool.update_order(
                                        f"update qingka_wangke_order set status = '等待中' , process = '0%',"
                                        f"remarks = '账号中存在进行中课程，等待其执行完毕后执行(请等待)......' "
                                        f"where oid = '{oid}'"
                                    )
                                    continue

                                # 提交任务到线程池
                                future = executor.submit(
                                    Run,
                                    oid,
                                    cid,
                                    school,
                                    username,
                                    password,
                                    kcid,
                                    pool,
                                )
                                futures[oid] = future

                                # 注册到活跃线程
                                ThreadManager.register_thread(oid, future)

                                # 每创建一个线程等待一段时间，避免瞬间创建大量线程
                                time.sleep(5)
                            except Exception as e:
                                logger.error(f"创建订单处理线程失败: {str(e)}")
                                traceback.print_exc()
                                # 释放可能已获取的锁
                                ThreadManager.account_unlock(username, cid)
                                continue

                # 每隔30秒获取一次新订单
                time.sleep(30)
            except Exception as e:
                logger.error(f"获取订单列表失败: {str(e)}")
                traceback.print_exc()
                time.sleep(30)  # 出错后等待30秒再继续


def Run(oid, cid, school, username, password, courseid, pool):
    """处理单个订单的任务函数"""
    try:
        logger.info(f"开始处理订单: OID={oid}, 用户={username}, 课程ID={courseid}")

        # 标记为进行中
        pool.update_order(
            f"update qingka_wangke_order set status = '进行中' , process = '0%',"
            f"remarks = '正在初始化会话...' where oid = '{oid}'"
        )

        # 初始化会话
        try:
            session = requests.session()
            session.headers.update(
                {"User-Agent": UA().WEB, "X-Requested-With": "XMLHttpRequest"}
            )
            logger.debug(f"ID:{username},创建请求会话成功")
        except Exception as e:
            logger.error(f"ID:{username},创建请求会话失败: {str(e)}")
            raise

        # 创建StartSession包装器
        try:
            logger.debug(f"ID:{username},开始创建StartSession包装器")
            session_wrapper = StartSession(session)
            logger.debug(f"ID:{username},创建StartSession包装器成功")
        except Exception as e:
            logger.error(f"ID:{username},创建StartSession包装器失败: {str(e)}")
            raise

        # 初始化主类
        try:
            logger.debug(f"ID:{username},开始创建MainXxt实例")
            MyXxt = MainXxt(
                session_wrapper, school, username, password, courseid, oid, cid, pool
            )
            logger.debug(f"ID:{username},创建MainXxt实例成功")
        except Exception as e:
            logger.error(f"ID:{username},创建MainXxt实例失败: {str(e)}")
            raise

        # 登录
        try:
            logger.debug(f"ID:{username},开始登录")
            login_result = MyXxt.fanyalogin()
            if login_result is True:
                logger.success(f"ID:{username},登录成功")
                pool.update_order(
                    f"update qingka_wangke_order set status = '进行中' , process = '0%',"
                    f"remarks = '【登录成功】 - 课件获取中......' where oid = '{oid}'"
                )

                # 获取课程列表
                course_result = MyXxt.kclist()
                if course_result is True:
                    logger.success(f"ID:{username},课程信息匹配成功")

                    # 获取课程章节
                    MyXxt.studentcourse()

                    # 开始学习
                    MyXxt.studentstudy()
                else:
                    logger.error(f"ID:{username},课程信息匹配失败")
                    pool.update_order(
                        f"update qingka_wangke_order set status = '异常' , process = '0%',"
                        f"remarks = '课程信息匹配失败' where oid = '{oid}'"
                    )
            else:
                logger.error(f"ID:{username},{login_result}")
                pool.update_order(
                    f"update qingka_wangke_order set status = '异常' , process = '0%',"
                    f"remarks = '{login_result}' where oid = '{oid}'"
                )
        except Exception as e:
            logger.error(f"ID:{username},登录或课程处理过程中出错: {str(e)}")
            raise

    except Exception as e:
        logger.error(f"ID:{username},执行异常: {str(e)}")
        traceback.print_exc()

        # 对异常消息进行处理，防止SQL注入
        error_msg = str(e)
        # 替换单引号为双引号，避免SQL注入
        error_msg = error_msg.replace("'", '"')
        # 限制错误消息长度
        if len(error_msg) > 200:
            error_msg = error_msg[:197] + "..."

        pool.update_order(
            f"update qingka_wangke_order set status = '异常' , process = '0%',"
            f"remarks = '执行异常: {error_msg}' where oid = '{oid}'"
        )
    finally:
        # 注销线程
        ThreadManager.unregister_thread(oid)
        # 确保账号锁被释放
        ThreadManager.account_unlock(username, cid)
        # 检查等待中的订单，将其设为待处理
        try:
            pool.update_order(
                f"update qingka_wangke_order set status = '待处理' , process = '0%',"
                f"remarks = '分配资源中......' where cid = '{cid}' and user = '{username}' and status = '等待中'"
            )
        except:
            pass


if __name__ == "__main__":
    try:
        # 配置日志
        logger.add(
            "logs/app_{time}.log", rotation="100 MB", retention="7 days", level="INFO"
        )
        logger.info("学习通自动化系统启动...")

        # 初始化数据库连接池
        pool = None
        max_retries = 5
        retry_count = 0

        while retry_count < max_retries:
            try:
                pool = OrderProcessorsql()
                logger.success("数据库连接池初始化成功")
                break
            except Exception as e:
                retry_count += 1
                logger.error(
                    f"数据库连接池初始化失败 (尝试 {retry_count}/{max_retries}): {str(e)}"
                )
                if retry_count >= max_retries:
                    logger.critical("数据库连接失败，程序退出")
                    sys.exit(1)
                time.sleep(5)  # 等待5秒后重试

        # 重置订单状态
        try:
            sql = """
                UPDATE qingka_wangke_order
                SET status = '待处理', process = '', remarks = ''
                WHERE cid IN (9000,9001,9002,9003,9004)
                AND status IN ('进行中', '等待中', '排队中', '停止中','休息中')
                """
            pool.update_order(sql)
            logger.success("数据状态重置完成")
        except Exception as e:
            logger.error(f"数据状态重置失败: {str(e)}")
            traceback.print_exc()

        # 启动监控线程
        def monitor_thread():
            """监控活跃线程数量"""
            while running:
                active_count = ThreadManager.get_active_count()
                logger.info(f"当前活跃线程数: {active_count}/{MAX_WORKERS}")
                time.sleep(60)  # 每分钟报告一次

        threading.Thread(target=monitor_thread, daemon=True).start()

        # 启动订单处理
        logger.info("开始处理订单...")
        order_get(pool)

    except KeyboardInterrupt:
        logger.warning("接收到键盘中断，正在安全退出...")
        running = False
    except Exception as e:
        logger.critical(f"主程序异常: {str(e)}")
        traceback.print_exc()
    finally:
        logger.info("程序退出")
