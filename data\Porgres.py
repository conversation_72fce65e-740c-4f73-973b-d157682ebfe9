import json
import re
import time
import traceback
from datetime import datetime

from bs4 import BeautifulSoup
from loguru import logger


def t():
    return int(time.time() * 1000)


class StartProces:
    def __init__(self, session, list_info):
        self.session = session
        self.kcname = list_info[0]["kcname"]
        self.clazzid = list_info[0]["clazzid"]
        self.cpi = list_info[0]["cpi"]
        self.courseid = list_info[0]["courseid"]

    def get_xxt_jxz(self):
        try:
            r = self.session.get(
                f"https://stat2-ans.chaoxing.com/study-data/index",
                params={
                    "courseid": self.courseid,
                    "clazzid": self.clazzid,
                    "cpi": self.cpi,
                    "ut": "s",
                    "t": t(),
                },
            ).text
            # 使用正则表达式匹配jobEnc的值
            res_html = BeautifulSoup(r, "html.parser")

            # 初始化变量，避免未定义错误
            self.pEnc = ""
            self.pEnc2 = ""

            # 尝试从多个可能的位置获取jobEnc
            jobEnc_found = False

            # 方法1: 尝试从第10个script标签获取
            try:
                all_scripts = res_html.findAll("script")
                if len(all_scripts) > 10:
                    txt = str(all_scripts[10])
                    match = re.search(r"var jobEnc = '([^']+)';", txt)
                    if match:
                        self.pEnc2 = match.group(1)
                        jobEnc_found = True
                        logger.debug("从第10个script标签找到jobEnc")
            except (IndexError, AttributeError) as e:
                logger.debug(f"从第10个script标签获取jobEnc失败: {str(e)}")

            # 方法2: 如果方法1失败，尝试从所有script标签中查找
            if not jobEnc_found:
                try:
                    for script in res_html.findAll("script"):
                        script_text = str(script)
                        match = re.search(r"var jobEnc = '([^']+)';", script_text)
                        if match:
                            self.pEnc2 = match.group(1)
                            jobEnc_found = True
                            logger.debug("从所有script标签中找到jobEnc")
                            break
                except Exception as e:
                    logger.debug(f"从所有script标签获取jobEnc失败: {str(e)}")

            # 尝试获取pEnc值
            try:
                penc_input = res_html.find("input", {"id": "pEnc"})
                if penc_input:
                    self.pEnc = penc_input.get("value")
                    logger.debug("找到pEnc输入值")
            except Exception as e:
                logger.debug(f"获取pEnc输入值失败: {str(e)}")

            # 如果没有找到pEnc，使用pEnc2作为备选
            if not self.pEnc and self.pEnc2:
                self.pEnc = self.pEnc2
                logger.debug("使用pEnc2作为pEnc的备选")

            # 如果仍然没有找到pEnc，尝试从URL参数中提取
            if not self.pEnc:
                try:
                    for a in res_html.findAll("a", href=True):
                        href = a.get("href", "")
                        if "pEnc" in href:
                            match = re.search(r"pEnc=([^&]+)", href)
                            if match:
                                self.pEnc = match.group(1)
                                logger.debug("从URL参数中提取pEnc")
                                break
                except Exception as e:
                    logger.debug(f"从URL参数提取pEnc失败: {str(e)}")

            # 如果仍然没有找到pEnc，使用默认值
            if not self.pEnc:
                self.pEnc = "0"
                logger.debug("使用默认pEnc值")

            # 获取视频信息
            try:
                video_lei = re.sub(
                    r"[\t\n\r 　 ]+",
                    "",
                    res_html.find("div", {"class": "list"})
                    .find("div", {"class": "centerC"})
                    .text.strip(),
                )
                video_zong = (
                    res_html.find("div", {"class": "list"})
                    .find("p", {"class": "bottomC fs12"})
                    .text.strip()
                )
            except AttributeError as e:
                logger.error(f"解析视频信息失败: {str(e)}")
                video_lei = "未知"
                video_zong = "未知"

            # 获取进度数据
            try:
                res_len = self.session.get(
                    "https://stat2-ans.chaoxing.com/stat2/study-data/job",
                    params={
                        "clazzid": self.clazzid,
                        "courseid": self.courseid,
                        "cpi": self.cpi,
                        "ut": "s",
                        "pEnc": self.pEnc,
                    },
                ).json()["data"]
                process = f"{res_len['jobPer']}%"
            except (KeyError, ValueError, TypeError, json.JSONDecodeError) as e:
                logger.error(f"获取进度数据失败: {str(e)}")
                # 尝试从页面中提取进度信息
                try:
                    progress_text = res_html.find("div", {"class": "progress-box"})
                    if progress_text:
                        progress_value = progress_text.find(
                            "span", {"class": "progressvalue"}
                        )
                        if progress_value:
                            process = f"{progress_value.text.strip()}%"
                        else:
                            process = "10%"
                    else:
                        process = "10%"
                except Exception:
                    process = "10%"

                # 构造一个基本的res_len字典，避免后续代码出错
                res_len = {"job": "?", "publishJobNum": "?"}
                return process, "进度获取失败 - 订单正常进行中......"

            try:
                cishu = (
                    res_html.find("div", {"class": "single-list mt-25"}).find("h2").text
                )
            except AttributeError:
                cishu = "未知"

            # 获取测验数据
            try:
                res_ce = BeautifulSoup(
                    self.session.get(
                        "https://stat2-ans.chaoxing.com/stat2/chapter-exam/s/index",
                        params={
                            "courseid": self.courseid,
                            "cpi": self.cpi,
                            "clazzid": self.clazzid,
                            "ut": "s",
                        },
                    ).text,
                    "html.parser",
                )

                ce_lei = (
                    res_ce.find("div", {"class": "user-info"})
                    .find("div", {"class": "leftC"})
                    .get("aria-label")
                    .strip()
                    .replace("完成进度;", "")
                )
            except (AttributeError, TypeError) as e:
                logger.error(f"获取测验数据失败: {str(e)}")
                ce_lei = "未知"

            # 构建进度信息
            self.video_jinxing = f"课程任务:{res_len.get('job', '?')}/{res_len.get('publishJobNum', '?')} | 章节测验:{ce_lei}"
            return process, self.video_jinxing
        except Exception as e:
            logger.error(f"进度获取异常: {str(e)}")
            traceback.print_exc()
            return "10%", "进度更新异常 - 订单正常进行中......"

    def get_xxt_wc(self):
        """
        检查课程考试完成状态
        返回值：
        - True: 课程已完成（没有考试或所有考试都已完成）
        - False: 课程未完成（存在待解答的考试）
        """
        try:
            res_exam = self.session.get(
                "https://stat2-ans.chaoxing.com/stat2/exam-stastics/stu-exams",
                params={
                    "clazzid": self.clazzid,
                    "courseid": self.courseid,
                    "cpi": self.cpi,
                    "ut": "s",
                    "pEnc": "",
                    "page": "1",
                    "pageSize": "999",
                    "personId": self.cpi,
                },
            ).json()["data"]

            # 如果没有考试数据，说明课程没有考试，返回已完成
            if not res_exam:
                logger.debug(f"课程无考试数据，标记为已完成")
                return True

            # 检查所有考试的状态
            pending_exams = []
            completed_exams = []

            for exam in res_exam:
                exam_title = exam.get("titleAll", "未知考试")
                exam_status = exam.get("statusStr", "未知状态")

                if exam_status == "待解答":
                    pending_exams.append(exam_title)
                else:
                    completed_exams.append(f"{exam_title}({exam_status})")

            # 记录考试状态信息
            if pending_exams:
                logger.info(f"发现待解答考试: {', '.join(pending_exams)}")
                if completed_exams:
                    logger.info(f"已完成考试: {', '.join(completed_exams)}")
                return False  # 有待解答的考试，课程未完成
            else:
                logger.info(f"所有考试已完成: {', '.join(completed_exams) if completed_exams else '无考试'}")
                return True  # 所有考试都已完成或无考试

        except Exception as e:
            logger.error(f"考试数据获取异常: {str(e)}")
            traceback.print_exc()
            # 出错时返回False，让系统继续尝试考试处理，避免跳过考试
            return False

    def get_platform_9004_progress(
        self, current_step=1, total_steps=1, homework_title="", status="进行中"
    ):
        """
        专门为平台ID 9004设计的进度跟踪方法

        Args:
            current_step: 当前完成的步骤数
            total_steps: 总步骤数
            homework_title: 当前处理的作业标题
            status: 当前状态

        Returns:
            tuple: (进度百分比, 进度详情)
        """
        try:
            # 计算进度百分比
            if status in [
                "异常",
                "参数不足",
                "未找到作业",
                "未找到作业URL",
                "作业URL无效",
                "处理异常",
            ]:
                progress_percent = 0  # 异常状态进度为0
            else:
                progress_percent = min(100, int((current_step / total_steps) * 100))

            # 格式化当前时间
            current_time = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

            # 处理作业标题
            display_title = homework_title
            # 如果标题是"作业任务_数字"格式，则尝试获取更有意义的标题
            if homework_title and re.match(r"^作业任务_\d+$", homework_title):
                # 提取ID部分
                work_id = homework_title.split("_")[1]
                # 保留原始格式，不再转换为"作业 (ID: XXX)"格式
                display_title = homework_title

            # 确保display_title不为空
            if not display_title or display_title.strip() == "":
                display_title = "作业任务"

            # 检查session中是否有AI状态信息
            ai_status_info = ""
            if hasattr(self.session, "ai_status_info") and self.session.ai_status_info:
                ai_status_info = self.session.ai_status_info

            # 构建进度详情
            if status in ["异常", "参数不足"]:
                progress_details = f"{display_title} | {status} | 进度: 0/{total_steps} | 未找到作业。更新: {current_time}"
            elif status in ["未找到作业", "未找到作业URL", "作业URL无效"]:
                progress_details = f"{display_title} | 参数有问题 | 进度: 0/{total_steps} | 未找到有效作业。更新: {current_time}"
            elif status == "处理异常":
                progress_details = f"{display_title} | {status} | 进度: 0/{total_steps} | 作业处理异常。更新: {current_time}"
            elif status == "已完成":
                # 如果有AI状态信息，添加到进度详情中
                if ai_status_info:
                    progress_details = f"{display_title} | {ai_status_info} | 进度: {current_step}/{total_steps} | 更新: {current_time}"
                elif "已提交过" in display_title:
                    progress_details = f"{display_title} | 状态: {status} | 进度: {current_step}/{total_steps} | 更新: {current_time}"
                else:
                    progress_details = f"{display_title} | 状态: {status} | 进度: {current_step}/{total_steps} | 更新: {current_time}"
            elif status == "已提交过":
                if ai_status_info:
                    progress_details = f"{display_title} | {ai_status_info} | 进度: {current_step}/{total_steps} | 更新: {current_time}"
                else:
                    progress_details = f"{display_title} | 状态: {status} | 进度: {current_step}/{total_steps} | 更新: {current_time}"
            else:
                # 在其他状态下也添加AI状态信息（如果有）
                if ai_status_info:
                    progress_details = f"{display_title} | {ai_status_info} | 进度: {current_step}/{total_steps} | 更新: {current_time}"
                else:
                    progress_details = f"{display_title} | {status} | 进度: {current_step}/{total_steps} | 更新: {current_time}"

            return f"{progress_percent}%", progress_details

        except Exception as e:
            logger.error(f"平台ID 9004进度计算异常: {str(e)}")
            return (
                "0%",
                f"作业任务处理异常 | 更新: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            )
